# PasteFlow Data Persistence Proposal v2
## Clean localStorage Elimination with SQLite Migration

_Version: 2.0_  
_Date: 2025-08-01_  
_Status: Revised per Technical Review_

## Executive Summary

This revised proposal addresses the directive to **immediately eliminate localStorage** from PasteFlow's architecture. We will implement a single SQLite database solution with a one-shot migration, providing improved performance, unlimited capacity, and enhanced security.

### Key Changes from v1
- **Single Database Architecture**: SQLite only (no hybrid approach)
- **Immediate Migration**: One-shot conversion on first launch
- **Extended Timeline**: 10-12 weeks with proper risk buffers
- **Enhanced Security**: Detailed SQLCipher integration and key management
- **Complete IPC Design**: Schema validation, throttling, and hardening

## 1. Architecture Overview

### 1.1 Single-Store Architecture

```
┌─────────────────────────────────────────────────────────┐
│                    Main Process                          │
├─────────────────────────────────────────────────────────┤
│                                                          │
│  ┌─────────────────────────────────────────────────┐    │
│  │            SQLite Database (pasteflow.db)        │    │
│  │                                                  │    │
│  │  Tables:                                         │    │
│  │  • workspaces (complete state serialization)     │    │
│  │  • files (metadata with content deduplication)   │    │
│  │  • file_contents (hashed, compressed storage)    │    │
│  │  • preferences (UI state, user settings)         │    │
│  │  • prompts (system/role prompts)                │    │
│  │  • instructions (docs, custom instructions)      │    │
│  │                                                  │    │
│  │  Features:                                       │    │
│  │  • SQLCipher encryption (256-bit AES)           │    │
│  │  • WAL mode for concurrent reads                │    │
│  │  • Automatic VACUUM and integrity checks        │    │
│  └─────────────────────────────────────────────────┘    │
│                          ↑                               │
│                          │                               │
│     ┌────────────────────┴────────────────────┐        │
│     │        Async Database Wrapper            │        │
│     │    (worker_threads for non-blocking)     │        │
│     └────────────────────────┬────────────────┘        │
│                              │                           │
│     ┌────────────────────────┴────────────────────┐    │
│     │         Secure IPC Layer                     │    │
│     │  • Zod schema validation                     │    │
│     │  • Rate limiting (100 req/s per channel)     │    │
│     │  • Origin verification                       │    │
│     │  • Input sanitization                        │    │
│     └────────────────────────┬────────────────────┘    │
│                              │                           │
├──────────────────────────────┼───────────────────────────┤
│                              ↓                           │
│                    Renderer Process                      │
│                                                          │
│  ┌─────────────────────────────────────────────────┐   │
│  │          React Hooks (IPC-backed)                │   │
│  │  • useAppState → IPC: /workspace/*               │   │
│  │  • useFileSelection → IPC: /file/*               │   │
│  │  • usePromptState → IPC: /prefs/prompts          │   │
│  │  • useWorkspaceState → IPC: /workspace/current   │   │
│  └─────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 1.2 Database Schema (Complete)

```sql
-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Version tracking for schema migrations
CREATE TABLE schema_version (
  version INTEGER PRIMARY KEY,
  applied_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Workspaces with complete state
CREATE TABLE workspaces (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  folder_path TEXT NOT NULL,
  state_json TEXT NOT NULL, -- Complete serialized WorkspaceState
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now')),
  last_accessed INTEGER DEFAULT (strftime('%s', 'now'))
);

-- File metadata with deduplication
CREATE TABLE files (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  path TEXT NOT NULL,
  workspace_id TEXT NOT NULL,
  content_hash TEXT,
  size INTEGER NOT NULL,
  is_binary BOOLEAN NOT NULL DEFAULT 0,
  token_count INTEGER,
  last_modified INTEGER,
  FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
  UNIQUE(path, workspace_id)
);

-- Deduplicated file contents
CREATE TABLE file_contents (
  hash TEXT PRIMARY KEY,
  content BLOB NOT NULL, -- Compressed with zlib
  original_size INTEGER NOT NULL,
  compressed_size INTEGER NOT NULL,
  compression_ratio REAL,
  created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- User preferences (replaces electron-settings)
CREATE TABLE preferences (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  encrypted BOOLEAN DEFAULT 0,
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- System and role prompts
CREATE TABLE prompts (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL CHECK(type IN ('system', 'role')),
  name TEXT NOT NULL,
  content TEXT NOT NULL,
  token_count INTEGER,
  is_active BOOLEAN DEFAULT 1,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Instructions and documentation
CREATE TABLE instructions (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  content TEXT NOT NULL,
  category TEXT,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Audit log for critical operations
CREATE TABLE audit_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  operation TEXT NOT NULL,
  table_name TEXT,
  record_id TEXT,
  old_value TEXT,
  new_value TEXT,
  timestamp INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Performance indexes
CREATE INDEX idx_files_workspace ON files(workspace_id);
CREATE INDEX idx_files_hash ON files(content_hash);
CREATE INDEX idx_workspaces_accessed ON workspaces(last_accessed DESC);
CREATE INDEX idx_audit_timestamp ON audit_log(timestamp DESC);

-- Triggers for auto-updating timestamps
CREATE TRIGGER update_workspace_timestamp 
AFTER UPDATE ON workspaces
BEGIN
  UPDATE workspaces SET updated_at = strftime('%s', 'now') 
  WHERE id = NEW.id;
END;

CREATE TRIGGER update_prompt_timestamp 
AFTER UPDATE ON prompts
BEGIN
  UPDATE prompts SET updated_at = strftime('%s', 'now') 
  WHERE id = NEW.id;
END;
```

## 2. Security Implementation

### 2.1 SQLCipher Integration

```typescript
// build/sqlcipher-setup.js
const Database = require('better-sqlite3');
const crypto = require('crypto');
const keytar = require('keytar');

class SecureDatabase {
  private db: Database.Database;
  private key: Buffer;

  constructor(dbPath: string) {
    this.key = await this.deriveEncryptionKey();
    
    this.db = new Database(dbPath);
    
    // Enable SQLCipher
    this.db.pragma(`cipher = 'aes-256-cbc'`);
    this.db.pragma(`key = '${this.key.toString('hex')}'`);
    
    // Security settings
    this.db.pragma('cipher_integrity_check = 1');
    this.db.pragma('cipher_memory_security = ON');
    
    // Performance settings
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('synchronous = NORMAL');
    this.db.pragma('cache_size = -64000'); // 64MB cache
  }

  private async deriveEncryptionKey(): Promise<Buffer> {
    // Get base secret from macOS Keychain
    const keychainSecret = await keytar.getPassword(
      'com.pasteflow.app',
      'db-encryption-key'
    );
    
    if (!keychainSecret) {
      // Generate new key on first run
      const newKey = crypto.randomBytes(32);
      await keytar.setPassword(
        'com.pasteflow.app',
        'db-encryption-key',
        newKey.toString('base64')
      );
      return newKey;
    }
    
    // Derive key with device-specific salt
    const deviceSalt = this.getDeviceSalt();
    return crypto.pbkdf2Sync(
      keychainSecret,
      deviceSalt,
      100000, // iterations
      32,     // key length
      'sha256'
    );
  }

  private getDeviceSalt(): Buffer {
    // Combine multiple device-specific identifiers
    const { machineId } = require('node-machine-id');
    const deviceId = machineId.machineIdSync();
    const username = require('os').userInfo().username;
    
    return crypto.createHash('sha256')
      .update(deviceId)
      .update(username)
      .update('pasteflow-v2')
      .digest();
  }
}
```

### 2.2 IPC Security Layer

```typescript
// src/main/ipc/validator.ts
import { z } from 'zod';
import { RateLimiter } from 'limiter';

// Schema definitions
const WorkspaceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  folderPath: z.string().regex(/^\/[^<>:"|?*]+$/), // Unix path validation
  state: z.record(z.unknown())
});

const FileOperationSchema = z.object({
  workspaceId: z.string().uuid(),
  filePath: z.string(),
  lineRanges: z.array(z.object({
    start: z.number().int().positive(),
    end: z.number().int().positive()
  })).optional()
});

// IPC channel registry with validators
const IPC_CHANNELS = {
  '/workspace/list': {
    input: z.object({}),
    output: z.array(WorkspaceSchema),
    rateLimit: 10 // requests per second
  },
  '/workspace/create': {
    input: WorkspaceSchema.omit({ id: true }),
    output: WorkspaceSchema,
    rateLimit: 5
  },
  '/workspace/load': {
    input: z.object({ id: z.string().uuid() }),
    output: WorkspaceSchema,
    rateLimit: 20
  },
  '/file/content': {
    input: FileOperationSchema,
    output: z.object({
      content: z.string(),
      tokenCount: z.number().int()
    }),
    rateLimit: 100
  },
  '/prefs/get': {
    input: z.object({ key: z.string() }),
    output: z.unknown(),
    rateLimit: 50
  },
  '/prefs/set': {
    input: z.object({
      key: z.string(),
      value: z.unknown(),
      encrypted: z.boolean().optional()
    }),
    output: z.boolean(),
    rateLimit: 20
  }
};

// Rate limiter setup
const rateLimiters = new Map<string, RateLimiter>();

Object.entries(IPC_CHANNELS).forEach(([channel, config]) => {
  rateLimiters.set(channel, new RateLimiter({
    tokensPerInterval: config.rateLimit,
    interval: 'second'
  }));
});

// Secure IPC handler wrapper
export function registerSecureHandler<T, R>(
  channel: keyof typeof IPC_CHANNELS,
  handler: (input: T) => Promise<R>
) {
  const config = IPC_CHANNELS[channel];
  const limiter = rateLimiters.get(channel);

  ipcMain.handle(channel, async (event, rawInput) => {
    // Origin verification
    if (!event.senderFrame.url.startsWith('file://')) {
      throw new Error('Invalid origin');
    }

    // Rate limiting
    if (!await limiter.tryRemoveTokens(1)) {
      throw new Error('Rate limit exceeded');
    }

    // Input validation
    const validatedInput = config.input.parse(rawInput);

    // Execute handler with validated input
    const result = await handler(validatedInput);

    // Output validation
    return config.output.parse(result);
  });
}
```

## 3. Migration Strategy

### 3.1 One-Shot Migration

```typescript
// src/main/migration/localStorage-to-sqlite.ts
export class LocalStorageMigrator {
  private db: SecureDatabase;
  private migrationLog: string[] = [];

  async migrate(): Promise<MigrationResult> {
    // Check if already migrated
    const migrated = this.db.get(
      'SELECT value FROM preferences WHERE key = ?',
      'migration_completed'
    );
    
    if (migrated) {
      return { success: true, alreadyMigrated: true };
    }

    try {
      // Start transaction
      this.db.exec('BEGIN IMMEDIATE');

      // Step 1: Extract all localStorage data via IPC
      const localData = await this.extractLocalStorageData();
      
      // Step 2: Validate and transform data
      const validated = this.validateLocalStorageData(localData);
      
      // Step 3: Migrate each data type
      await this.migrateWorkspaces(validated.workspaces);
      await this.migratePrompts(validated.prompts);
      await this.migratePreferences(validated.preferences);
      await this.migrateInstructions(validated.instructions);
      
      // Step 4: Mark migration complete
      this.db.run(
        'INSERT INTO preferences (key, value) VALUES (?, ?)',
        'migration_completed',
        new Date().toISOString()
      );
      
      // Step 5: Create backup of localStorage before deletion
      await this.createLocalStorageBackup(localData);
      
      // Commit transaction
      this.db.exec('COMMIT');
      
      // Step 6: Delete localStorage keys
      await this.deleteLocalStorageKeys();
      
      return {
        success: true,
        itemsMigrated: this.migrationLog.length,
        log: this.migrationLog
      };
      
    } catch (error) {
      // Rollback on any error
      this.db.exec('ROLLBACK');
      
      return {
        success: false,
        error: error.message,
        log: this.migrationLog
      };
    }
  }

  private async extractLocalStorageData(): Promise<LocalStorageData> {
    // Request data from renderer via special IPC channel
    const { ipcMain } = require('electron');
    
    return new Promise((resolve) => {
      ipcMain.once('migration:localStorage-data', (event, data) => {
        resolve(data);
      });
      
      // Request data from all renderer windows
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('migration:request-localStorage');
      });
    });
  }

  private validateLocalStorageData(data: any): ValidatedData {
    // Strict validation with fallbacks
    const validated: ValidatedData = {
      workspaces: {},
      prompts: { system: [], role: [] },
      preferences: {},
      instructions: []
    };

    // Validate workspaces
    if (data[STORAGE_KEYS.WORKSPACES]) {
      try {
        const workspaces = JSON.parse(data[STORAGE_KEYS.WORKSPACES]);
        for (const [name, state] of Object.entries(workspaces)) {
          if (this.isValidWorkspaceState(state)) {
            validated.workspaces[name] = state;
            this.migrationLog.push(`Validated workspace: ${name}`);
          }
        }
      } catch (e) {
        this.migrationLog.push('ERROR: Failed to parse workspaces');
      }
    }

    // Continue for other data types...
    return validated;
  }

  private async createLocalStorageBackup(data: LocalStorageData) {
    const backupPath = path.join(
      app.getPath('userData'),
      'backups',
      `localStorage-backup-${Date.now()}.json`
    );
    
    await fs.promises.mkdir(path.dirname(backupPath), { recursive: true });
    await fs.promises.writeFile(
      backupPath,
      JSON.stringify(data, null, 2),
      { mode: 0o600 } // Read/write for owner only
    );
    
    this.migrationLog.push(`Backup created: ${backupPath}`);
  }
}
```

### 3.2 Migration UI

```typescript
// src/renderer/components/MigrationDialog.tsx
export const MigrationDialog: React.FC = () => {
  const [status, setStatus] = useState<MigrationStatus>('pending');
  const [progress, setProgress] = useState(0);
  const [log, setLog] = useState<string[]>([]);

  useEffect(() => {
    const runMigration = async () => {
      setStatus('running');
      
      // Subscribe to progress updates
      ipcRenderer.on('migration:progress', (_, data) => {
        setProgress(data.percent);
        setLog(prev => [...prev, data.message]);
      });

      try {
        const result = await ipcRenderer.invoke('migration:start');
        
        if (result.success) {
          setStatus('success');
          // Reload app with new data layer
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          setStatus('error');
          console.error('Migration failed:', result.error);
        }
      } catch (error) {
        setStatus('error');
      }
    };

    runMigration();
  }, []);

  return (
    <div className="migration-overlay">
      <div className="migration-dialog">
        <h2>Upgrading PasteFlow Database</h2>
        
        {status === 'running' && (
          <>
            <ProgressBar value={progress} />
            <p>Migrating your data to the new storage system...</p>
            <div className="migration-log">
              {log.map((entry, i) => (
                <div key={i}>{entry}</div>
              ))}
            </div>
          </>
        )}

        {status === 'success' && (
          <div className="success-message">
            ✓ Migration completed successfully!
            <p>Restarting with improved performance...</p>
          </div>
        )}

        {status === 'error' && (
          <div className="error-message">
            ✗ Migration encountered an error
            <p>Your data is safe. Please restart the app.</p>
            <button onClick={() => app.quit()}>Quit</button>
          </div>
        )}
      </div>
    </div>
  );
};
```

## 4. Performance Optimizations

### 4.1 Async Database Wrapper

```typescript
// src/main/db/async-wrapper.ts
import { Worker } from 'worker_threads';
import { EventEmitter } from 'events';

export class AsyncDatabase extends EventEmitter {
  private worker: Worker;
  private requestId = 0;
  private pendingRequests = new Map();

  constructor(dbPath: string) {
    super();
    
    // Create worker thread for database operations
    this.worker = new Worker('./db-worker.js', {
      workerData: { dbPath }
    });

    this.worker.on('message', ({ id, result, error }) => {
      const pending = this.pendingRequests.get(id);
      if (pending) {
        if (error) {
          pending.reject(new Error(error));
        } else {
          pending.resolve(result);
        }
        this.pendingRequests.delete(id);
      }
    });
  }

  // Async wrapper for all database operations
  async run(sql: string, params?: any[]): Promise<any> {
    return this.sendToWorker('run', { sql, params });
  }

  async get(sql: string, params?: any[]): Promise<any> {
    return this.sendToWorker('get', { sql, params });
  }

  async all(sql: string, params?: any[]): Promise<any[]> {
    return this.sendToWorker('all', { sql, params });
  }

  // Prepared statements for performance
  async prepare(sql: string): Promise<PreparedStatement> {
    const stmtId = await this.sendToWorker('prepare', { sql });
    return new PreparedStatement(this, stmtId);
  }

  private sendToWorker(method: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = ++this.requestId;
      this.pendingRequests.set(id, { resolve, reject });
      this.worker.postMessage({ id, method, params });
    });
  }
}

// db-worker.js
const { parentPort, workerData } = require('worker_threads');
const Database = require('better-sqlite3');

const db = new Database(workerData.dbPath);
const statements = new Map();

parentPort.on('message', ({ id, method, params }) => {
  try {
    let result;
    
    switch (method) {
      case 'run':
        result = db.prepare(params.sql).run(...(params.params || []));
        break;
      
      case 'get':
        result = db.prepare(params.sql).get(...(params.params || []));
        break;
      
      case 'all':
        result = db.prepare(params.sql).all(...(params.params || []));
        break;
      
      case 'prepare':
        const stmt = db.prepare(params.sql);
        const stmtId = `stmt_${Date.now()}_${Math.random()}`;
        statements.set(stmtId, stmt);
        result = stmtId;
        break;
    }
    
    parentPort.postMessage({ id, result });
  } catch (error) {
    parentPort.postMessage({ id, error: error.message });
  }
});
```

### 4.2 Content Deduplication

```typescript
// src/main/db/content-deduplication.ts
import { createHash } from 'crypto';
import { deflateSync, inflateSync } from 'zlib';

export class ContentDeduplicator {
  private db: AsyncDatabase;

  async storeFileContent(
    content: string,
    filePath: string
  ): Promise<string> {
    // Calculate content hash
    const hash = createHash('sha256')
      .update(content)
      .digest('hex');

    // Check if content already exists
    const existing = await this.db.get(
      'SELECT hash FROM file_contents WHERE hash = ?',
      [hash]
    );

    if (!existing) {
      // Compress content
      const compressed = deflateSync(content);
      const compressionRatio = compressed.length / content.length;

      // Store new content
      await this.db.run(`
        INSERT INTO file_contents 
        (hash, content, original_size, compressed_size, compression_ratio)
        VALUES (?, ?, ?, ?, ?)
      `, [
        hash,
        compressed,
        content.length,
        compressed.length,
        compressionRatio
      ]);

      // Log space savings
      console.log(`Stored ${filePath}: ${Math.round((1 - compressionRatio) * 100)}% compressed`);
    } else {
      console.log(`Deduplicated ${filePath}: content already stored`);
    }

    return hash;
  }

  async retrieveContent(hash: string): Promise<string> {
    const row = await this.db.get(
      'SELECT content FROM file_contents WHERE hash = ?',
      [hash]
    );

    if (!row) {
      throw new Error(`Content not found: ${hash}`);
    }

    // Decompress and return
    return inflateSync(row.content).toString('utf8');
  }
}
```

## 5. Implementation Timeline (Revised)

### 5.1 10-Week Development Schedule

| Week | Phase | Tasks | Deliverables |
|------|-------|-------|--------------|
| 1 | **Research & Design** | - Finalize schema design<br>- PoC benchmarks<br>- SQLCipher build testing<br>- Team alignment | - Technical specification<br>- Performance baseline |
| 2-3 | **Core Infrastructure** | - Async database wrapper<br>- IPC layer with Zod<br>- Security implementation<br>- Basic CRUD operations | - Database module<br>- IPC validators<br>- Unit tests |
| 4-5 | **Hook Migration** | - Replace localStorage hooks<br>- Implement IPC-backed state<br>- Maintain API compatibility<br>- Integration testing | - Migrated hooks<br>- Backward compatible API |
| 6 | **Migration System** | - One-shot migration script<br>- Backup system<br>- Migration UI<br>- Error recovery | - Migration tool<br>- User documentation |
| 7 | **Feature Parity** | - Complete functionality testing<br>- Edge case handling<br>- Performance profiling<br>- Bug fixes | - Feature complete build<br>- QA report |
| 8 | **Performance Tuning** | - Query optimization<br>- Index tuning<br>- WAL checkpointing<br>- Memory management | - Performance metrics<br>- Optimization report |
| 9 | **Security Hardening** | - SQLCipher integration<br>- Key management<br>- IPC validation<br>- Penetration testing | - Security audit<br>- Hardened build |
| 10 | **Release Preparation** | - Beta testing<br>- CI/CD updates<br>- Notarization<br>- Documentation | - Release candidate<br>- Deployment guide |

### 5.2 Risk Buffers

- **Week 11-12**: Buffer for unexpected issues
- **Parallel QA**: Continuous testing from Week 4
- **Rollback Plan**: Keep localStorage code in separate branch

## 6. Missing Implementation Details

### 6.1 CI/CD Pipeline Updates

```yaml
# .github/workflows/build.yml additions
- name: Install SQLCipher dependencies
  run: |
    brew install sqlcipher
    npm install --build-from-source better-sqlite3

- name: Code sign native modules
  run: |
    codesign --force --deep --sign "$CERTIFICATE" \
      node_modules/better-sqlite3/build/Release/better_sqlite3.node

- name: Notarization size check
  run: |
    SIZE_BEFORE=$(stat -f%z dist/PasteFlow-before.app)
    SIZE_AFTER=$(stat -f%z dist/PasteFlow.app)
    DELTA=$((SIZE_AFTER - SIZE_BEFORE))
    echo "Size increase: $((DELTA / 1048576))MB"
    if [ $DELTA -gt 10485760 ]; then
      echo "Warning: Size increased by more than 10MB"
    fi
```

### 6.2 Automated Testing Strategy

```typescript
// src/__tests__/db-migration.test.ts
describe('Database Migration', () => {
  let testDb: AsyncDatabase;
  let migrator: LocalStorageMigrator;

  beforeEach(async () => {
    // Create test database
    testDb = new AsyncDatabase(':memory:');
    await initializeSchema(testDb);
    
    // Mock localStorage data
    mockLocalStorage({
      [STORAGE_KEYS.WORKSPACES]: JSON.stringify(testWorkspaces),
      [STORAGE_KEYS.PROMPTS]: JSON.stringify(testPrompts)
    });
    
    migrator = new LocalStorageMigrator(testDb);
  });

  it('should migrate all workspace data', async () => {
    const result = await migrator.migrate();
    
    expect(result.success).toBe(true);
    
    const workspaces = await testDb.all('SELECT * FROM workspaces');
    expect(workspaces).toHaveLength(testWorkspaces.length);
    
    // Verify data integrity
    for (const workspace of workspaces) {
      const state = JSON.parse(workspace.state_json);
      expect(state).toMatchObject(expect.objectContaining({
        selectedFiles: expect.any(Array),
        expandedNodes: expect.any(Object)
      }));
    }
  });

  it('should handle corrupted localStorage gracefully', async () => {
    mockLocalStorage({
      [STORAGE_KEYS.WORKSPACES]: 'invalid json{{'
    });
    
    const result = await migrator.migrate();
    
    expect(result.success).toBe(true);
    expect(result.log).toContain('ERROR: Failed to parse workspaces');
  });

  it('should create backup before deletion', async () => {
    const backupSpy = jest.spyOn(migrator, 'createLocalStorageBackup');
    
    await migrator.migrate();
    
    expect(backupSpy).toHaveBeenCalled();
    
    const backupPath = backupSpy.mock.results[0].value;
    expect(await fs.pathExists(backupPath)).toBe(true);
  });
});
```

### 6.3 Performance Monitoring

```typescript
// src/main/db/performance-monitor.ts
export class DatabasePerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  
  async measureQuery<T>(
    operation: string,
    query: () => Promise<T>
  ): Promise<T> {
    const start = performance.now();
    
    try {
      const result = await query();
      const duration = performance.now() - start;
      
      this.recordMetric(operation, {
        duration,
        timestamp: Date.now(),
        success: true
      });
      
      // Alert if query exceeds budget
      if (duration > 50) {
        console.warn(`Slow query detected: ${operation} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      
      this.recordMetric(operation, {
        duration,
        timestamp: Date.now(),
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }

  getMetrics(operation?: string): PerformanceReport {
    if (operation) {
      return this.generateReport(this.metrics.get(operation) || []);
    }
    
    // Aggregate all metrics
    const allMetrics: PerformanceMetric[] = [];
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    
    return this.generateReport(allMetrics);
  }

  private generateReport(metrics: PerformanceMetric[]): PerformanceReport {
    if (metrics.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0, p95: 0 };
    }
    
    const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
    
    return {
      count: metrics.length,
      avg: durations.reduce((a, b) => a + b, 0) / durations.length,
      min: durations[0],
      max: durations[durations.length - 1],
      p95: durations[Math.floor(durations.length * 0.95)]
    };
  }
}
```

## 7. Edge Cases & Error Recovery

### 7.1 Database Corruption Recovery

```typescript
// src/main/db/integrity-checker.ts
export class DatabaseIntegrityChecker {
  async checkIntegrity(): Promise<IntegrityReport> {
    const results = await this.db.all('PRAGMA integrity_check');
    
    if (results[0].integrity_check !== 'ok') {
      // Attempt recovery
      console.error('Database corruption detected:', results);
      
      try {
        // Try to recover what we can
        await this.recoverDatabase();
      } catch (error) {
        // Fall back to restore from backup
        await this.restoreFromBackup();
      }
    }
    
    return {
      healthy: results[0].integrity_check === 'ok',
      details: results
    };
  }

  private async recoverDatabase() {
    // Create new database
    const recoveryDb = new AsyncDatabase(':memory:');
    await initializeSchema(recoveryDb);
    
    // Copy table by table
    const tables = ['workspaces', 'files', 'prompts', 'preferences'];
    
    for (const table of tables) {
      try {
        const rows = await this.db.all(`SELECT * FROM ${table}`);
        for (const row of rows) {
          await this.insertRow(recoveryDb, table, row);
        }
      } catch (error) {
        console.error(`Failed to recover table ${table}:`, error);
      }
    }
    
    // Replace corrupted database
    await this.replaceDatabase(recoveryDb);
  }
}
```

### 7.2 Long Transaction Handling

```typescript
// src/main/db/transaction-manager.ts
export class TransactionManager {
  async executeWithProgress<T>(
    operation: string,
    steps: TransactionStep[],
    onProgress: (percent: number) => void
  ): Promise<T> {
    const checkpoint = await this.createCheckpoint();
    
    try {
      await this.db.run('BEGIN IMMEDIATE');
      
      let completed = 0;
      const results = [];
      
      for (const step of steps) {
        // Check for cancellation
        if (this.isCancelled(operation)) {
          throw new Error('Operation cancelled');
        }
        
        // Execute step
        const result = await step.execute();
        results.push(result);
        
        // Update progress
        completed++;
        onProgress((completed / steps.length) * 100);
      }
      
      await this.db.run('COMMIT');
      return results as T;
      
    } catch (error) {
      await this.db.run('ROLLBACK');
      await this.restoreCheckpoint(checkpoint);
      throw error;
    }
  }
}
```

### 7.3 File System Synchronization

```typescript
// src/main/db/fs-sync.ts
export class FileSystemSync {
  async handleFileMove(oldPath: string, newPath: string) {
    await this.db.run(`
      UPDATE files 
      SET path = ?
      WHERE path = ?
    `, [newPath, oldPath]);
    
    // Update audit log
    await this.db.run(`
      INSERT INTO audit_log (operation, table_name, old_value, new_value)
      VALUES ('file_move', 'files', ?, ?)
    `, [oldPath, newPath]);
  }

  async handleFileDelete(filePath: string) {
    // Don't delete content (other files might reference it)
    await this.db.run(`
      UPDATE files
      SET content_hash = NULL
      WHERE path = ?
    `, [filePath]);
    
    // Clean up orphaned content periodically
    await this.scheduleContentCleanup();
  }

  private async scheduleContentCleanup() {
    // Run cleanup in background
    setTimeout(async () => {
      await this.db.run(`
        DELETE FROM file_contents
        WHERE hash NOT IN (
          SELECT DISTINCT content_hash 
          FROM files 
          WHERE content_hash IS NOT NULL
        )
      `);
    }, 60000); // 1 minute delay
  }
}
```

## 8. Development Guidelines

### 8.1 Code Standards

```typescript
// DO: Use the centralized database module
import { db } from '@/main/database';

const workspace = await db.workspace.get(id);

// DON'T: Direct database access
const db = new Database('pasteflow.db');
const workspace = db.prepare('SELECT * FROM workspaces WHERE id = ?').get(id);

// DO: Use validated IPC calls
const result = await ipcRenderer.invoke('/workspace/create', {
  name: 'My Workspace',
  folderPath: '/path/to/folder'
});

// DON'T: Unvalidated IPC
const result = await ipcRenderer.invoke('create-workspace', data);

// DO: Handle errors gracefully
try {
  const content = await db.file.getContent(hash);
} catch (error) {
  if (error.code === 'CONTENT_NOT_FOUND') {
    // Handle missing content
  }
  // Log and show user-friendly error
}
```

### 8.2 Testing Requirements

All database operations must have:
- Unit tests with in-memory database
- Integration tests with file-based database
- Performance benchmarks
- Error case coverage

### 8.3 Security Checklist

- [ ] All IPC channels use Zod validation
- [ ] File paths are validated against workspace boundaries
- [ ] Database encryption enabled in production
- [ ] Sensitive data marked for encryption
- [ ] No SQL injection vulnerabilities
- [ ] Rate limiting on all IPC channels

## 9. Monitoring & Telemetry

```typescript
// src/main/telemetry/db-metrics.ts
export class DatabaseMetrics {
  private metrics = {
    queries: new Map<string, number>(),
    errors: new Map<string, number>(),
    performance: new Map<string, number[]>()
  };

  recordQuery(operation: string, duration: number) {
    // Increment counter
    this.metrics.queries.set(
      operation,
      (this.metrics.queries.get(operation) || 0) + 1
    );
    
    // Track performance
    const perfs = this.metrics.performance.get(operation) || [];
    perfs.push(duration);
    this.metrics.performance.set(operation, perfs);
    
    // Send to telemetry service (if enabled)
    if (this.telemetryEnabled) {
      this.sendTelemetry({
        event: 'db_query',
        properties: {
          operation,
          duration,
          timestamp: Date.now()
        }
      });
    }
  }

  getReport(): MetricsReport {
    const report: MetricsReport = {
      totalQueries: 0,
      averageQueryTime: 0,
      slowestOperations: [],
      errorRate: 0
    };
    
    // Calculate metrics
    for (const [op, count] of this.metrics.queries) {
      report.totalQueries += count;
      
      const perfs = this.metrics.performance.get(op) || [];
      if (perfs.length > 0) {
        const avg = perfs.reduce((a, b) => a + b, 0) / perfs.length;
        report.slowestOperations.push({ operation: op, avgTime: avg });
      }
    }
    
    report.slowestOperations.sort((a, b) => b.avgTime - a.avgTime);
    report.slowestOperations = report.slowestOperations.slice(0, 10);
    
    return report;
  }
}
```

## 10. Conclusion

This v2 proposal addresses all technical review concerns:

1. **Single Database Architecture** - SQLite only, no hybrid complexity
2. **Immediate Migration** - One-shot conversion, localStorage eliminated completely
3. **Realistic Timeline** - 10-12 weeks with proper risk buffers
4. **Enhanced Security** - Detailed SQLCipher integration and IPC hardening
5. **Complete Implementation** - Async wrapper, validation, monitoring, and recovery

The revised approach provides a cleaner architecture, faster migration, and more robust implementation while meeting the directive to eliminate localStorage immediately.