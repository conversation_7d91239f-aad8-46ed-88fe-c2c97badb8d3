# PasteFlow SQLite Migration - Phase 1: Foundation & Infrastructure
## Database Setup, Async Wrapper, and IPC Layer Implementation

_Implementation Phase: 1 of 4_  
_Duration: Weeks 1-3_  
_Status: Foundation Phase_

## Executive Summary

This document details Phase 1 of the PasteFlow SQLite migration, focusing on establishing the foundational infrastructure. This phase creates the core database layer, implements the async wrapper for non-blocking operations, and establishes the secure IPC communication layer with validation. All subsequent phases depend on the successful completion of this foundation.

## Overall Project Context

### Project Overview
PasteFlow is migrating from localStorage (16 keys, 5-10MB limits) to a SQLite-based architecture to eliminate performance bottlenecks, remove storage limitations, and improve security. The migration follows a 4-phase approach over 10-12 weeks.

### Complete Phase Overview
1. **Phase 1: Foundation & Infrastructure** (Weeks 1-3) - *THIS PHASE*
2. **Phase 2: State Management Migration** (Weeks 4-5)
3. **Phase 3: Data Migration & Recovery** (Weeks 6-7)
4. **Phase 4: Optimization & Release** (Weeks 8-10)

### Phase 1 Goals
- Establish SQLite database with SQLCipher encryption
- Implement async database wrapper with worker threads
- Create secure IPC layer with Zod validation
- Set up core CRUD operations
- Establish security foundations

## Week 1: Research & Design

### 1.1 Schema Finalization

Create the complete database schema file:

```typescript
// src/main/db/schema.sql
-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Version tracking for future migrations
CREATE TABLE schema_version (
  version INTEGER PRIMARY KEY,
  applied_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Workspaces with complete state serialization
CREATE TABLE workspaces (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  folder_path TEXT NOT NULL,
  state_json TEXT NOT NULL, -- Complete serialized WorkspaceState
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now')),
  last_accessed INTEGER DEFAULT (strftime('%s', 'now'))
);

-- File metadata with content deduplication
CREATE TABLE files (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  path TEXT NOT NULL,
  workspace_id TEXT NOT NULL,
  content_hash TEXT,
  size INTEGER NOT NULL,
  is_binary BOOLEAN NOT NULL DEFAULT 0,
  token_count INTEGER,
  last_modified INTEGER,
  FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
  UNIQUE(path, workspace_id)
);

-- Deduplicated file contents with compression
CREATE TABLE file_contents (
  hash TEXT PRIMARY KEY,
  content BLOB NOT NULL, -- Compressed with zlib
  original_size INTEGER NOT NULL,
  compressed_size INTEGER NOT NULL,
  compression_ratio REAL,
  created_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- User preferences (replaces electron-settings)
CREATE TABLE preferences (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  encrypted BOOLEAN DEFAULT 0,
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- System and role prompts
CREATE TABLE prompts (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL CHECK(type IN ('system', 'role')),
  name TEXT NOT NULL,
  content TEXT NOT NULL,
  token_count INTEGER,
  is_active BOOLEAN DEFAULT 1,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Instructions and documentation
CREATE TABLE instructions (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  content TEXT NOT NULL,
  category TEXT,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Audit log for critical operations
CREATE TABLE audit_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  operation TEXT NOT NULL,
  table_name TEXT,
  record_id TEXT,
  old_value TEXT,
  new_value TEXT,
  timestamp INTEGER DEFAULT (strftime('%s', 'now'))
);

-- Performance indexes
CREATE INDEX idx_files_workspace ON files(workspace_id);
CREATE INDEX idx_files_hash ON files(content_hash);
CREATE INDEX idx_workspaces_accessed ON workspaces(last_accessed DESC);
CREATE INDEX idx_audit_timestamp ON audit_log(timestamp DESC);

-- Auto-update triggers
CREATE TRIGGER update_workspace_timestamp 
AFTER UPDATE ON workspaces
BEGIN
  UPDATE workspaces SET updated_at = strftime('%s', 'now') 
  WHERE id = NEW.id;
END;

CREATE TRIGGER update_prompt_timestamp 
AFTER UPDATE ON prompts
BEGIN
  UPDATE prompts SET updated_at = strftime('%s', 'now') 
  WHERE id = NEW.id;
END;

-- Insert initial schema version
INSERT INTO schema_version (version) VALUES (1);
```

### 1.2 SQLCipher Build Testing

Set up SQLCipher compilation with better-sqlite3:

```bash
# build/setup-sqlcipher.sh
#!/bin/bash

# Install SQLCipher dependencies
if [[ "$OSTYPE" == "darwin"* ]]; then
  brew install sqlcipher
else
  sudo apt-get install libsqlcipher-dev
fi

# Build better-sqlite3 with SQLCipher
npm install better-sqlite3 --build-from-source --sqlite3="$(brew --prefix sqlcipher)"

# Verify SQLCipher support
node -e "
const Database = require('better-sqlite3');
const db = new Database(':memory:');
console.log('SQLCipher test:', db.pragma('cipher_version'));
"
```

### 1.3 Performance Benchmarks

Create benchmark suite to establish baseline:

```typescript
// src/main/db/__tests__/benchmarks.ts
import { performance } from 'perf_hooks';

export class DatabaseBenchmarks {
  private results: BenchmarkResult[] = [];

  async runAllBenchmarks() {
    await this.benchmarkInserts();
    await this.benchmarkReads();
    await this.benchmarkTransactions();
    await this.benchmarkConcurrentReads();
    
    return this.generateReport();
  }

  private async benchmarkInserts() {
    const db = new Database(':memory:');
    await initializeSchema(db);

    const start = performance.now();
    const stmt = db.prepare(
      'INSERT INTO files (path, workspace_id, size, is_binary) VALUES (?, ?, ?, ?)'
    );

    const transaction = db.transaction((files) => {
      for (const file of files) {
        stmt.run(file.path, file.workspaceId, file.size, file.isBinary);
      }
    });

    // Test with 10,000 files
    const testFiles = Array.from({ length: 10000 }, (_, i) => ({
      path: `/test/file${i}.ts`,
      workspaceId: 'test-workspace',
      size: Math.floor(Math.random() * 100000),
      isBinary: false
    }));

    transaction(testFiles);
    const duration = performance.now() - start;

    this.results.push({
      operation: 'insert_10k_files',
      duration,
      opsPerSecond: 10000 / (duration / 1000)
    });
  }

  private async benchmarkReads() {
    // Benchmark various read patterns
    const queries = [
      {
        name: 'workspace_load',
        sql: 'SELECT * FROM workspaces WHERE id = ?',
        params: ['test-workspace']
      },
      {
        name: 'file_search',
        sql: 'SELECT * FROM files WHERE workspace_id = ? AND path LIKE ?',
        params: ['test-workspace', '%component%']
      },
      {
        name: 'content_retrieval',
        sql: 'SELECT content FROM file_contents WHERE hash = ?',
        params: ['test-hash']
      }
    ];

    for (const query of queries) {
      const start = performance.now();
      const stmt = this.db.prepare(query.sql);
      
      // Run 1000 times
      for (let i = 0; i < 1000; i++) {
        stmt.get(...query.params);
      }
      
      const duration = performance.now() - start;
      this.results.push({
        operation: query.name,
        duration,
        opsPerSecond: 1000 / (duration / 1000)
      });
    }
  }
}
```

## Week 2-3: Core Infrastructure

### 2.1 Async Database Wrapper

Implement the worker-thread based async wrapper:

```typescript
// src/main/db/async-database.ts
import { Worker } from 'worker_threads';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';

interface WorkerRequest {
  id: string;
  method: string;
  params: any;
}

interface WorkerResponse {
  id: string;
  result?: any;
  error?: string;
}

export class AsyncDatabase extends EventEmitter {
  private worker: Worker;
  private pendingRequests = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();

  constructor(dbPath: string) {
    super();
    
    // Create worker thread
    this.worker = new Worker(
      path.join(__dirname, 'database-worker.js'),
      {
        workerData: { dbPath }
      }
    );

    // Handle worker messages
    this.worker.on('message', (response: WorkerResponse) => {
      const pending = this.pendingRequests.get(response.id);
      if (pending) {
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(response.id);
        
        if (response.error) {
          pending.reject(new Error(response.error));
        } else {
          pending.resolve(response.result);
        }
      }
    });

    // Handle worker errors
    this.worker.on('error', (error) => {
      console.error('Database worker error:', error);
      this.handleWorkerError(error);
    });
  }

  // Core database operations
  async run(sql: string, params?: any[]): Promise<any> {
    return this.sendToWorker('run', { sql, params });
  }

  async get(sql: string, params?: any[]): Promise<any> {
    return this.sendToWorker('get', { sql, params });
  }

  async all(sql: string, params?: any[]): Promise<any[]> {
    return this.sendToWorker('all', { sql, params });
  }

  async exec(sql: string): Promise<void> {
    return this.sendToWorker('exec', { sql });
  }

  // Transaction support
  async transaction<T>(fn: () => Promise<T>): Promise<T> {
    await this.exec('BEGIN IMMEDIATE');
    try {
      const result = await fn();
      await this.exec('COMMIT');
      return result;
    } catch (error) {
      await this.exec('ROLLBACK');
      throw error;
    }
  }

  // Prepared statements
  async prepare(sql: string): Promise<PreparedStatement> {
    const stmtId = await this.sendToWorker('prepare', { sql });
    return new PreparedStatement(this, stmtId);
  }

  private sendToWorker(method: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = uuidv4();
      
      // Set timeout for long-running queries
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(id);
        reject(new Error(`Database operation timed out: ${method}`));
      }, 30000); // 30 second timeout

      this.pendingRequests.set(id, { resolve, reject, timeout });
      
      const request: WorkerRequest = { id, method, params };
      this.worker.postMessage(request);
    });
  }

  private handleWorkerError(error: Error) {
    // Reject all pending requests
    for (const [id, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error(`Worker terminated: ${error.message}`));
    }
    this.pendingRequests.clear();
    
    // Attempt to restart worker
    this.restartWorker();
  }

  private async restartWorker() {
    console.log('Attempting to restart database worker...');
    // Implementation depends on application requirements
  }

  async close() {
    await this.worker.terminate();
  }
}

// Prepared statement wrapper
export class PreparedStatement {
  constructor(
    private db: AsyncDatabase,
    private stmtId: string
  ) {}

  async run(...params: any[]): Promise<any> {
    return this.db.sendToWorker('stmt_run', { 
      stmtId: this.stmtId, 
      params 
    });
  }

  async get(...params: any[]): Promise<any> {
    return this.db.sendToWorker('stmt_get', { 
      stmtId: this.stmtId, 
      params 
    });
  }

  async all(...params: any[]): Promise<any[]> {
    return this.db.sendToWorker('stmt_all', { 
      stmtId: this.stmtId, 
      params 
    });
  }

  async finalize(): Promise<void> {
    return this.db.sendToWorker('stmt_finalize', { 
      stmtId: this.stmtId 
    });
  }
}
```

### 2.2 Database Worker Implementation

```typescript
// src/main/db/database-worker.js
const { parentPort, workerData } = require('worker_threads');
const Database = require('better-sqlite3');
const crypto = require('crypto');

// Initialize database with SQLCipher
const db = new Database(workerData.dbPath);

// Enable SQLCipher encryption
if (workerData.encryptionKey) {
  db.pragma(`cipher = 'aes-256-cbc'`);
  db.pragma(`key = '${workerData.encryptionKey}'`);
  db.pragma('cipher_integrity_check = 1');
  db.pragma('cipher_memory_security = ON');
}

// Performance settings
db.pragma('journal_mode = WAL');
db.pragma('synchronous = NORMAL');
db.pragma('cache_size = -64000'); // 64MB cache
db.pragma('temp_store = MEMORY');

// Prepared statement cache
const statements = new Map();

// Message handler
parentPort.on('message', ({ id, method, params }) => {
  try {
    let result;
    
    switch (method) {
      case 'run':
        result = db.prepare(params.sql).run(...(params.params || []));
        break;
      
      case 'get':
        result = db.prepare(params.sql).get(...(params.params || []));
        break;
      
      case 'all':
        result = db.prepare(params.sql).all(...(params.params || []));
        break;
      
      case 'exec':
        db.exec(params.sql);
        result = null;
        break;
      
      case 'prepare':
        const stmt = db.prepare(params.sql);
        const stmtId = `stmt_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
        statements.set(stmtId, stmt);
        result = stmtId;
        break;
      
      case 'stmt_run':
        result = statements.get(params.stmtId).run(...params.params);
        break;
      
      case 'stmt_get':
        result = statements.get(params.stmtId).get(...params.params);
        break;
      
      case 'stmt_all':
        result = statements.get(params.stmtId).all(...params.params);
        break;
      
      case 'stmt_finalize':
        statements.delete(params.stmtId);
        result = null;
        break;
      
      default:
        throw new Error(`Unknown method: ${method}`);
    }
    
    parentPort.postMessage({ id, result });
  } catch (error) {
    parentPort.postMessage({ id, error: error.message });
  }
});

// Periodic maintenance
setInterval(() => {
  try {
    // WAL checkpoint
    db.pragma('wal_checkpoint(TRUNCATE)');
    
    // Clean up old prepared statements
    const now = Date.now();
    for (const [id, stmt] of statements) {
      const timestamp = parseInt(id.split('_')[1]);
      if (now - timestamp > 300000) { // 5 minutes
        statements.delete(id);
      }
    }
  } catch (error) {
    console.error('Maintenance error:', error);
  }
}, 60000); // Every minute
```

### 2.3 Security Implementation

```typescript
// src/main/db/secure-database.ts
import { AsyncDatabase } from './async-database';
import * as keytar from 'keytar';
import * as crypto from 'crypto';
import { machineIdSync } from 'node-machine-id';
import * as os from 'os';

export class SecureDatabase {
  private db: AsyncDatabase;
  private encryptionKey: string;

  static async create(dbPath: string): Promise<SecureDatabase> {
    const instance = new SecureDatabase();
    await instance.initialize(dbPath);
    return instance;
  }

  private async initialize(dbPath: string) {
    // Derive encryption key
    this.encryptionKey = await this.deriveEncryptionKey();
    
    // Create encrypted database
    this.db = new AsyncDatabase(dbPath, {
      encryptionKey: this.encryptionKey
    });
    
    // Initialize schema if needed
    await this.initializeSchema();
  }

  private async deriveEncryptionKey(): Promise<string> {
    const SERVICE_NAME = 'com.pasteflow.app';
    const ACCOUNT_NAME = 'db-encryption-key';
    
    // Try to get existing key from macOS Keychain
    let baseSecret = await keytar.getPassword(SERVICE_NAME, ACCOUNT_NAME);
    
    if (!baseSecret) {
      // Generate new key on first run
      const newKey = crypto.randomBytes(32);
      baseSecret = newKey.toString('base64');
      
      // Store in Keychain
      await keytar.setPassword(SERVICE_NAME, ACCOUNT_NAME, baseSecret);
      
      console.log('Generated new database encryption key');
    }
    
    // Derive key with device-specific salt
    const deviceSalt = this.getDeviceSalt();
    const derivedKey = crypto.pbkdf2Sync(
      baseSecret,
      deviceSalt,
      100000,  // iterations
      32,      // key length
      'sha256'
    );
    
    return derivedKey.toString('hex');
  }

  private getDeviceSalt(): Buffer {
    // Combine multiple device-specific identifiers
    const deviceId = machineIdSync();
    const username = os.userInfo().username;
    const hostname = os.hostname();
    
    return crypto.createHash('sha256')
      .update(deviceId)
      .update(username)
      .update(hostname)
      .update('pasteflow-v2-salt')
      .digest();
  }

  private async initializeSchema() {
    // Check if schema exists
    const version = await this.db.get(
      'SELECT version FROM schema_version ORDER BY version DESC LIMIT 1'
    ).catch(() => null);
    
    if (!version) {
      // Load and execute schema
      const schemaSQL = await fs.readFile(
        path.join(__dirname, 'schema.sql'),
        'utf8'
      );
      
      await this.db.exec(schemaSQL);
      console.log('Database schema initialized');
    }
  }

  // Delegate database operations
  get database(): AsyncDatabase {
    return this.db;
  }
}
```

### 2.4 IPC Layer Implementation

```typescript
// src/main/ipc/secure-ipc.ts
import { ipcMain, IpcMainInvokeEvent } from 'electron';
import { z, ZodSchema } from 'zod';
import { RateLimiter } from 'limiter';

// IPC channel configuration
interface IpcChannelConfig<TInput, TOutput> {
  input: ZodSchema<TInput>;
  output: ZodSchema<TOutput>;
  rateLimit: number;
  handler?: (input: TInput, event: IpcMainInvokeEvent) => Promise<TOutput>;
}

export class SecureIpcLayer {
  private channels = new Map<string, IpcChannelConfig<any, any>>();
  private rateLimiters = new Map<string, RateLimiter>();

  constructor() {
    this.setupChannels();
  }

  private setupChannels() {
    // Define all IPC channels with their schemas
    this.registerChannel('/workspace/list', {
      input: z.object({}),
      output: z.array(WorkspaceSchema),
      rateLimit: 10
    });

    this.registerChannel('/workspace/create', {
      input: WorkspaceCreateSchema,
      output: WorkspaceSchema,
      rateLimit: 5
    });

    this.registerChannel('/workspace/load', {
      input: z.object({ id: z.string().uuid() }),
      output: WorkspaceSchema,
      rateLimit: 20
    });

    this.registerChannel('/workspace/update', {
      input: WorkspaceUpdateSchema,
      output: z.boolean(),
      rateLimit: 10
    });

    this.registerChannel('/workspace/delete', {
      input: z.object({ id: z.string().uuid() }),
      output: z.boolean(),
      rateLimit: 5
    });

    this.registerChannel('/file/content', {
      input: FileContentRequestSchema,
      output: FileContentResponseSchema,
      rateLimit: 100
    });

    this.registerChannel('/file/save', {
      input: FileSaveSchema,
      output: z.boolean(),
      rateLimit: 50
    });

    this.registerChannel('/prefs/get', {
      input: z.object({ key: z.string() }),
      output: z.unknown(),
      rateLimit: 50
    });

    this.registerChannel('/prefs/set', {
      input: PreferenceSetSchema,
      output: z.boolean(),
      rateLimit: 20
    });
  }

  registerChannel<TInput, TOutput>(
    channel: string,
    config: IpcChannelConfig<TInput, TOutput>
  ) {
    this.channels.set(channel, config);
    
    // Create rate limiter
    this.rateLimiters.set(channel, new RateLimiter({
      tokensPerInterval: config.rateLimit,
      interval: 'second',
      fireImmediately: true
    }));

    // Register IPC handler
    ipcMain.handle(channel, async (event, rawInput) => {
      try {
        // Security checks
        await this.performSecurityChecks(channel, event);
        
        // Rate limiting
        await this.checkRateLimit(channel);
        
        // Input validation
        const validatedInput = config.input.parse(rawInput);
        
        // Execute handler
        let result: TOutput;
        if (config.handler) {
          result = await config.handler(validatedInput, event);
        } else {
          // Default handler (to be overridden)
          throw new Error(`No handler registered for ${channel}`);
        }
        
        // Output validation
        return config.output.parse(result);
        
      } catch (error) {
        console.error(`IPC error on ${channel}:`, error);
        
        if (error instanceof z.ZodError) {
          throw new Error(`Validation error: ${error.message}`);
        }
        
        throw error;
      }
    });
  }

  private async performSecurityChecks(
    channel: string,
    event: IpcMainInvokeEvent
  ) {
    // Verify origin
    const url = event.senderFrame.url;
    if (!url.startsWith('file://')) {
      throw new Error('Invalid origin');
    }

    // Additional checks can be added here
    // - CSRF tokens
    // - Session validation
    // - Permission checks
  }

  private async checkRateLimit(channel: string) {
    const limiter = this.rateLimiters.get(channel);
    if (!limiter) {
      throw new Error(`No rate limiter for channel: ${channel}`);
    }

    const hasToken = await limiter.tryRemoveTokens(1);
    if (!hasToken) {
      throw new Error('Rate limit exceeded');
    }
  }

  // Set handler for a channel
  setHandler<TInput, TOutput>(
    channel: string,
    handler: (input: TInput, event: IpcMainInvokeEvent) => Promise<TOutput>
  ) {
    const config = this.channels.get(channel);
    if (!config) {
      throw new Error(`Unknown channel: ${channel}`);
    }
    
    config.handler = handler;
  }
}
```

### 2.5 Schema Definitions

```typescript
// src/main/ipc/schemas.ts
import { z } from 'zod';

// Workspace schemas
export const WorkspaceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  folderPath: z.string(),
  state: z.record(z.unknown()),
  createdAt: z.number(),
  updatedAt: z.number(),
  lastAccessed: z.number()
});

export const WorkspaceCreateSchema = z.object({
  name: z.string().min(1).max(255),
  folderPath: z.string(),
  state: z.record(z.unknown()).optional()
});

export const WorkspaceUpdateSchema = z.object({
  id: z.string().uuid(),
  state: z.record(z.unknown())
});

// File schemas
export const FileContentRequestSchema = z.object({
  workspaceId: z.string().uuid(),
  filePath: z.string(),
  lineRanges: z.array(z.object({
    start: z.number().int().positive(),
    end: z.number().int().positive()
  })).optional()
});

export const FileContentResponseSchema = z.object({
  content: z.string(),
  tokenCount: z.number().int(),
  hash: z.string(),
  compressed: z.boolean()
});

export const FileSaveSchema = z.object({
  workspaceId: z.string().uuid(),
  filePath: z.string(),
  content: z.string(),
  tokenCount: z.number().int().optional()
});

// Preference schemas
export const PreferenceSetSchema = z.object({
  key: z.string(),
  value: z.unknown(),
  encrypted: z.boolean().optional().default(false)
});

// Prompt schemas
export const PromptSchema = z.object({
  id: z.string(),
  type: z.enum(['system', 'role']),
  name: z.string(),
  content: z.string(),
  tokenCount: z.number().int().optional(),
  isActive: z.boolean()
});
```

## Testing Requirements

### Unit Tests

```typescript
// src/main/db/__tests__/async-database.test.ts
describe('AsyncDatabase', () => {
  let db: AsyncDatabase;

  beforeEach(async () => {
    db = new AsyncDatabase(':memory:');
    await db.exec(schemaSQL);
  });

  afterEach(async () => {
    await db.close();
  });

  it('should execute basic CRUD operations', async () => {
    // Insert
    const result = await db.run(
      'INSERT INTO workspaces (id, name, folder_path, state_json) VALUES (?, ?, ?, ?)',
      ['test-id', 'Test Workspace', '/test/path', '{}']
    );
    
    expect(result.changes).toBe(1);
    
    // Read
    const workspace = await db.get(
      'SELECT * FROM workspaces WHERE id = ?',
      ['test-id']
    );
    
    expect(workspace.name).toBe('Test Workspace');
    
    // Update
    await db.run(
      'UPDATE workspaces SET name = ? WHERE id = ?',
      ['Updated Workspace', 'test-id']
    );
    
    // Delete
    await db.run(
      'DELETE FROM workspaces WHERE id = ?',
      ['test-id']
    );
  });

  it('should handle transactions correctly', async () => {
    let error: Error | null = null;
    
    try {
      await db.transaction(async () => {
        await db.run('INSERT INTO workspaces (id, name) VALUES (?, ?)', 
          ['test1', 'Test 1']);
        
        // This should fail due to constraint
        await db.run('INSERT INTO workspaces (id, name) VALUES (?, ?)', 
          ['test1', 'Test 1']);
      });
    } catch (e) {
      error = e as Error;
    }
    
    expect(error).not.toBeNull();
    
    // Verify rollback
    const count = await db.get('SELECT COUNT(*) as count FROM workspaces');
    expect(count.count).toBe(0);
  });

  it('should handle concurrent operations', async () => {
    const operations = Array.from({ length: 100 }, (_, i) => 
      db.run(
        'INSERT INTO files (path, workspace_id, size, is_binary) VALUES (?, ?, ?, ?)',
        [`/file${i}.ts`, 'test-workspace', 1000, 0]
      )
    );
    
    const results = await Promise.all(operations);
    
    expect(results).toHaveLength(100);
    expect(results.every(r => r.changes === 1)).toBe(true);
  });
});
```

### Security Tests

```typescript
// src/main/db/__tests__/security.test.ts
describe('Database Security', () => {
  it('should encrypt database with SQLCipher', async () => {
    const db = await SecureDatabase.create(':memory:');
    
    // Verify encryption is enabled
    const cipherVersion = await db.database.get("SELECT sqlite_compileoption_get('CIPHER_VERSION') as version");
    expect(cipherVersion.version).toBeTruthy();
  });

  it('should derive unique keys per device', async () => {
    const db1 = await SecureDatabase.create(':memory:');
    const db2 = await SecureDatabase.create(':memory:');
    
    // Keys should be consistent for same device
    expect(db1.encryptionKey).toBe(db2.encryptionKey);
  });

  it('should store keys securely in Keychain', async () => {
    const password = await keytar.getPassword('com.pasteflow.app', 'db-encryption-key');
    expect(password).toBeTruthy();
  });
});
```

## Dependencies on Other Phases

This foundation phase provides:
- **For Phase 2**: Database operations API, IPC channels for hooks
- **For Phase 3**: Migration infrastructure, backup/restore capabilities
- **For Phase 4**: Performance monitoring hooks, optimization points

## Success Criteria

### Week 1 Completion
- [ ] Database schema finalized and reviewed
- [ ] SQLCipher successfully compiled with better-sqlite3
- [ ] Performance benchmarks established
- [ ] Security architecture documented

### Week 2-3 Completion
- [ ] Async database wrapper fully implemented
- [ ] All IPC channels defined with Zod schemas
- [ ] Rate limiting functional on all channels
- [ ] Core CRUD operations tested
- [ ] Security layer operational
- [ ] Unit tests achieving >90% coverage
- [ ] Integration tests passing

### Performance Targets
- Database initialization: <100ms
- Simple queries: <5ms
- Complex queries: <50ms
- Concurrent operation support: 100+ simultaneous queries
- Memory usage: <100MB for typical workload

### Security Checklist
- [ ] SQLCipher encryption enabled
- [ ] Keys stored in macOS Keychain
- [ ] IPC validation on all channels
- [ ] Rate limiting prevents abuse
- [ ] No SQL injection vulnerabilities
- [ ] Origin verification active

## Next Phase Preview

Phase 2 will build upon this foundation to:
- Migrate all localStorage hooks to use the new database
- Maintain backward compatibility during transition
- Implement caching strategies
- Create unified state management layer

The foundation established in Phase 1 is critical for all subsequent phases. Ensure all success criteria are met before proceeding to Phase 2.